package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
public class StoreProviderRelationVO {

    @Schema(description = "主单跟踪单号")
    private String mainEntrustedNo;

    @Schema(description = "服务商名称")
    private String providerName;

    @Schema(description = "服务商代码")
    @NotNull
    private String providerCode;

    @Schema(description = "服务商方式")
    private String providerServiceWay;

    @Schema(description = "运输时效")
    private String providerTransportTime;

    @Schema(description = "运费$")
    private BigDecimal freightAmount;

    @Schema(description = "保费$")
    private BigDecimal insuranceAmount;

    @Schema(description = "POD/面签费$")
    private BigDecimal podAmount;

    @Schema(description = "税费$")
    private BigDecimal taxAmount;
}
