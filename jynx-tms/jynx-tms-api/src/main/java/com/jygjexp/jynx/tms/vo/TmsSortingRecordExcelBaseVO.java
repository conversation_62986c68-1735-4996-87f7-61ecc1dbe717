package com.jygjexp.jynx.tms.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class TmsSortingRecordExcelBaseVO {

    @ExcelProperty(value = "扫描时间")
    @Schema(description="扫描时间")
    private LocalDateTime scanTime;

    @ExcelProperty(value = "格口")
    @Schema(description="格口")
    private String grid;

    @ExcelProperty(value = "分拣单号")
    @Schema(description="分拣单号")
    private String orderNo;

    @ExcelProperty(value = "机器编码")
    @Schema(description="机器编码")
    private String machineNumber;

    @ExcelProperty(value = "扫描重量（kg）")
    @Schema(description="扫描重量（kg）")
    private BigDecimal packageWeight;

    @ExcelProperty(value = "扫描体积（m³）")
    @Schema(description="扫描体积（m³）")
    private BigDecimal packageVolume;

    /**
     * 长（CM）
     */
    @ExcelProperty("长（CM）")
    @Schema(description="长（CM）")
    private BigDecimal packageLength;

    /**
     * 宽（CM）
     */
    @ExcelProperty("宽（CM）")
    @Schema(description="宽（CM）")
    private BigDecimal packageWidth;

    /**
     * 高（CM）
     */
    @ExcelProperty("高（CM）")
    @Schema(description="高（CM）")
    private BigDecimal packageHeight;

    @ExcelProperty(value = "分拣类型")
    @Schema(description="分拣类型")
    private String type;
}
