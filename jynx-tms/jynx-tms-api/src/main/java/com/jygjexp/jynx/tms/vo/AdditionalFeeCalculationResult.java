package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 附加费计算结果
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "附加费计算结果")
public class AdditionalFeeCalculationResult {

    /**
     * 附加费总额
     */
    @Schema(description = "附加费总额(CAD)")
    private BigDecimal totalFee;

    /**
     * 附加费明细列表
     */
    @Schema(description = "附加费明细列表")
    private List<String> feeRuleIds;

    /**
     * 附加费明细字符串（逗号分隔的规则ID）
     */
    @Schema(description = "附加费明细字符串，多个附加费项目使用逗号分隔")
    private String surchargeDetail;

    public AdditionalFeeCalculationResult() {
        this.totalFee = BigDecimal.ZERO;
        this.feeRuleIds = new ArrayList<>();
        this.surchargeDetail = "";
    }

    /**
     * 添加附加费项目
     *
     * @param feeRuleId 附加费规则ID
     * @param feeAmount 附加费金额
     */
    public void addFeeItem(String feeRuleId, BigDecimal feeAmount) {
        if (feeAmount != null && feeAmount.compareTo(BigDecimal.ZERO) > 0) {
            this.totalFee = this.totalFee.add(feeAmount);
            this.feeRuleIds.add(feeRuleId);
            updateSurchargeDetail();
        }
    }

    /**
     * 更新附加费明细字符串
     */
    private void updateSurchargeDetail() {
        this.surchargeDetail = String.join(",", this.feeRuleIds);
    }
}
