package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格计算详情
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@Schema(description = "价格计算详情")
public class PriceCalculationDetailVo {

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long orderId;

    /**
     * 客户订单号
     */
    @Schema(description = "客户订单号")
    private String customerOrderNumber;

    /**
     * 委托订单号
     */
    @Schema(description = "委托订单号")
    private String entrustedOrderNumber;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 计算是否成功
     */
    @Schema(description = "计算是否成功")
    private Boolean success;

    /**
     * 错误信息（计算失败时）
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 匹配的服务商名称
     */
    @Schema(description = "匹配的服务商名称")
    private String matchedProviderName;

    /**
     * 匹配的报价ID
     */
    @Schema(description = "匹配的报价ID")
    private Long matchedQuoteId;

    /**
     * 匹配的报价名称
     */
    @Schema(description = "匹配的报价名称")
    private String matchedQuoteName;

    /**
     * 匹配的区域名称
     */
    @Schema(description = "匹配的纵区名称")
    private String matchedRegionName;

    /**
     * 匹配的邮政编码前缀
     */
    @Schema(description = "匹配的邮政编码前缀")
    private String matchedPostalPrefix;

    /**
     * 区域类型（Zone 1: 同城, Zone 2: 跨城市）
     */
    @Schema(description = "区域类型")
    private String zoneType;

    /**
     * 订单重量
     */
    @Schema(description = "订单重量(kg)")
    private BigDecimal orderWeight;

    /**
     * 入库时间
     */
    @Schema(description = "入库时间")
    private LocalDateTime scanTime;


    /**
     * 订单体积
     */
    @Schema(description = "预报体积")
    private BigDecimal volume;

    /**
     * 预报重量
     */
    @Schema(description = "预报重量")
    private BigDecimal weight;



    /**
     * 实际重量（从货物信息表汇总的reviewWeight）
     */
    @Schema(description = "实际重量(kg)")
    private BigDecimal actualWeight;

    /**
     * 体积重（计算得出的体积重）
     */
    @Schema(description = "体积重(kg)")
    private BigDecimal volumeWeight;

    /**
     * 最终用于匹配的重量（实际重量和体积重取大值）
     */
    @Schema(description = "最终用于匹配的重量(kg)")
    private BigDecimal finalWeightForMatching;

    /**
     * 匹配的重量段起始值
     */
    @Schema(description = "匹配的重量段起始值(kg)")
    private BigDecimal matchedWeightStart;

    /**
     * 匹配的重量段结束值
     */
    @Schema(description = "匹配的重量段结束值(kg)")
    private BigDecimal matchedWeightEnd;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格(CAD)")
    private BigDecimal basePrice;

    /**
     * 附加费
     */
    @Schema(description = "附加费(CAD)")
    private BigDecimal additionalFee;

    /**
     * 附加费明细
     */
    @Schema(description = "附加费明细，多个附加费项目使用逗号分隔")
    private String surchargeDetail;

    /**
     * 利润率
     */
    @Schema(description = "利润率(%)")
    private BigDecimal profitRate;

    /**
     * 最终计算价格（基础价格 + 利润率）
     */
    @Schema(description = "最终计算价格(CAD)")
    private BigDecimal finalPrice;

    /**
     * 发件人邮政编码
     */
    @Schema(description = "发件人邮政编码")
    private String shipperPostalCode;

    /**
     * 收件人邮政编码
     */
    @Schema(description = "收件人邮政编码")
    private String destPostalCode;

    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    private String shipperCity;

    /**
     * 收件人城市
     */
    @Schema(description = "收件人城市")
    private String destCity;
}
