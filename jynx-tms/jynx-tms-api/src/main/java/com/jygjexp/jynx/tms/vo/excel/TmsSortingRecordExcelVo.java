package com.jygjexp.jynx.tms.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class TmsSortingRecordExcelVo {

    /**
     * 模版名称
     */
    @ExcelProperty("模版名称")
    @Schema(description="模版名称")
    private String templateName;

    /**
     * 格口
     */
    @ExcelProperty("格口")
    @Schema(description="格口")
    private String grid;

    /**
     * 分拣单号
     */
    @ExcelProperty("分拣单号")
    @Schema(description="分拣单号")
    private String orderNo;

    /**
     * 分拣扫描时间
     */
    @ExcelProperty("分拣扫描时间")
    @Schema(description="分拣扫描时间")
    private LocalDateTime scanTime;

    /**
     * 分拣状态
     */
    @ExcelProperty("分拣状态")
    @Schema(description="分拣状态")
    private String sortingStatus;

    /**
     * 路线号
     */
    @ExcelProperty("路线号")
    @Schema(description="路线号")
    private String routeNumber;

    /**
     * 扫描重量（kg）
     */
    @ExcelProperty("扫描重量（kg）")
    @Schema(description="扫描重量（kg）")
    private BigDecimal packageWeight;

    /**
     * 扫描体积（m³）
     */
    @ExcelProperty("扫描体积（m³）")
    @Schema(description="扫描体积（m³）")
    private BigDecimal packageVolume;

    /**
     * 长（CM）
     */
    @ExcelProperty("长（CM）")
    @Schema(description="长（CM）")
    private BigDecimal packageLength;

    /**
     * 宽（CM）
     */
    @ExcelProperty("宽（CM）")
    @Schema(description="宽（CM）")
    private BigDecimal packageWidth;

    /**
     * 高（CM）
     */
    @ExcelProperty("高（CM）")
    @Schema(description="高（CM）")
    private BigDecimal packageHeight;

    /**
     * 城市
     */
    @ExcelProperty("城市")
    @Schema(description="城市")
    private String city;
    /**
     * 机器编号
     */
    @ExcelProperty("机器编号")
    @Schema(description="机器编号")
    private String machineNumber;

    /**
     * 商家
     */
    @ExcelProperty("商家")
    @Schema(description="商家对应")
    private String merchant;

    /**
     * 分拣类型（0：揽收退件、1：正向派送）
     */
    @ExcelProperty("分拣类型")
    @Schema(description="分拣类型（0：揽收退件、1：正向派送）")
    private String type;
    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    @Schema(description="失败原因")
    private String failureReason;

}
