package com.jygjexp.jynx.tms.api.controller;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.vo.PodVo;
import com.jygjexp.jynx.tms.vo.SubOrderPodVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 获取面单相关信息
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tms/label")
@Tag(description = "获取面单相关信息", name = "面单打印获取面单信息")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LabelPrintController {

    private final TmsCustomerOrderService tmsCustomerOrderService;
    private final TmsOrderTrackService tmsOrderTrackService;

    @Inner(value = false)
    @Operation(summary = "根据主单号或客户单号获取客户订单信息", description = "根据单号获取客户订单信息")
    @GetMapping("/getCustomerOrder")
    public TmsCustomerOrderEntity getCustomerOrder(String trackingNo,Boolean isReferenceNumber) {
        return tmsCustomerOrderService.getByCustomerOrderNumber(trackingNo, isReferenceNumber);
    }


    @Inner(value = false)
    @Operation(summary = "根据子单号获取订单信息(主要用于面单)", description = "根据子单号获取订单信息(主要用于面单)")
    @GetMapping("/getCustomerOrderBySubOrder")
    public TmsCustomerOrderEntity getCustomerOrderBySubOrder(String trackingNo) {
        return tmsCustomerOrderService.getCustomerOrderBySubOrder(trackingNo);
    }

    @Inner(value = false)
    @Operation(summary = "获取订单POD信息", description = "获取订单POD信息")
    @GetMapping("/getPod")
    public PodVo getCustomerOrderPod(String trackingNo) {
        return tmsCustomerOrderService.getCustomerOrderPod(trackingNo);
    }


    @Inner(value = false)
    @Operation(summary = "获取子单POD信息", description = "获取子单POD信息")
    @GetMapping("/getSubOrderPod")
    public List<SubOrderPodVo> getSubOrderPod(String trackingNo) {
        return tmsCustomerOrderService.getCustomerSubOrderPod(trackingNo);
    }

    @Inner(value = false)
    @Operation(summary = "获取UPS轨迹信息", description = "获取UPS轨迹信息")
    @GetMapping("/getTrack")
    public String getTrack(String inquiryNumber) {
        return tmsOrderTrackService.queryUpsTrackingForJson(inquiryNumber);
    }

}
