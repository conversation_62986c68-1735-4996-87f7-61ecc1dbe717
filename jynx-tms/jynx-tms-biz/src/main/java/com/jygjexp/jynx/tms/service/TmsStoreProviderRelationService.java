package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsStoreProviderRelationEntity;

import java.util.List;
import java.util.Map;

public interface TmsStoreProviderRelationService extends IService<TmsStoreProviderRelationEntity> {

    void saveStoreOrderProvider(TmsStoreProviderRelationEntity saveBean);

    TmsStoreProviderRelationEntity getByMainEntrustedOrder(String mainEntrustedOrder);

    Map<String, TmsStoreProviderRelationEntity> getRelationMap(List<String> mainEntrustedOrders);

    void deleteByMainEntrustedOrder(String mainEntrustedOrder);

    void saveStoreOrderProviderBatch(List<TmsStoreProviderRelationEntity> storeProviderRelations);
}
