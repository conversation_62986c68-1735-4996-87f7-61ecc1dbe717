package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.admin.api.dto.ResetLoginPwdDTO;
import com.jygjexp.jynx.admin.api.dto.SysUserAddDto;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.feign.annotation.NoToken;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsStoreCustomerService;
import com.jygjexp.jynx.tms.dto.StorePwdChangeDTO;
import com.jygjexp.jynx.tms.dto.TmsStoreCustomerDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreCustomerEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreCustomerService;
import com.jygjexp.jynx.tms.vo.StoreCustomerExtendVO;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerPageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 门店客户表
 *
 * <AUTHOR>
 * @date 2025-07-14 17:45:27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreCustomer" )
@Tag(description = "tmsStoreCustomer" , name = "门店客户表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreCustomerController {

    private final  TmsStoreCustomerService service;
    private final RemoteTmsUpmsService remoteTmsUpmsService;

    /**
     * 通过id查询门店客户表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(service.getById(id));
    }

    /**
     * 新增门店客户表
     * @param tmsStoreCustomer 门店客户表
     * @return R
     */
    @Operation(summary = "新增门店客户表" , description = "新增门店客户表" )
    @SysLog("新增门店客户表" )
    @PostMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_add')" )
    public R save(@RequestBody TmsStoreCustomerEntity tmsStoreCustomer) {
        return R.ok(service.save(tmsStoreCustomer));
    }

    /**
     * 修改门店客户表
     * @param tmsStoreCustomer 门店客户表
     * @return R
     */
    @Operation(summary = "修改门店客户表" , description = "修改门店客户表" )
    @SysLog("修改门店客户表" )
    @PutMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_edit')" )
    public R updateById(@RequestBody TmsStoreCustomerEntity tmsStoreCustomer) {
        return R.ok(service.updateById(tmsStoreCustomer));
    }

    /**
     * 通过id删除门店客户表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店客户表" , description = "通过id删除门店客户表" )
    @SysLog("通过id删除门店客户表" )
    @DeleteMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return service.removeBatchCustomer(ids);
    }


    @Operation(summary = "查询通知配置" , description = "查询通知配置" )
    @GetMapping("/get_message_config" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_message_config')" )
    public R getMessageTraceConfig() {
        return R.ok(service.getMessageTraceConfig());
    }

    @Operation(summary = "修改通知配置" , description = "修改通知配置" )
    @PostMapping("/update_message_config" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_message_config')")
    public R updateMessageTraceConfig(@RequestBody StoreCustomerExtendVO customerExtendVO) {
        return R.ok(service.updateMessageTraceConfig(customerExtendVO));
    }

    @SysLog("获取邮箱验证码")
    @GetMapping("/getEmailCode")
    public R getEmailCode(@RequestParam(value = "oldEmail") String email) {
        return R.ok(service.getEmailCode(email));
    }

    @SysLog("修改邮箱")
    @PutMapping("/updateEmailCode")
    public R updateEmail(@RequestParam(value = "newEmail",required = true) String email,
                             @RequestParam(value = "code") String code) {
        return R.ok(service.updateEmail(email,code));
    }

    @SysLog("获取手机号验证码")
    @GetMapping("/getPhoneCode")
    @Ignore
    @NoToken
    public R getPhoneCode(@RequestParam(value = "oldPhone") String oldPhone) {
        return R.ok(service.getPhoneCode(oldPhone));
    }


    @SysLog("修改手机号")
    @PutMapping("/getPhoneCode")
    public R updatePhoneCode(@RequestParam(value = "newPhone") String newPhone,
                             @RequestParam(value = "code") String code) {
        return R.ok(service.updatePhone(newPhone,code));
    }

    @SysLog("登录页-修改密码")
    @PutMapping("/login/resetLoginPwd")
    @Ignore
    @NoToken
    public R resetLoginPwd(@RequestBody @Valid ResetLoginPwdDTO resetPwdDTO) {
        return R.ok(service.updateLoginPwd(resetPwdDTO));
    }

    @SysLog("修改密码")
    @PutMapping("/updatePwd")
    public R updatePwd(@RequestBody StorePwdChangeDTO storePwdChangeDTO) {
        return R.ok(service.updatePwd(storePwdChangeDTO));
    }


    /**
     * 门店端分页查询
     * @param page 门店端分页对象
     * @param tmsStoreCustomer 门店客户表
     * @return
     */
    @Operation(summary = "门店端分页查询" , description = "门店端分页查询" )
    @SysLog("门店端分页查询")
    @GetMapping("/store/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_view')" )
    public R getTmsStoreCustomerPage(@ParameterObject Page page, @ParameterObject TmsStoreCustomerPageVo tmsStoreCustomer) {
        boolean hasSearchCondition =
                StrUtil.isNotBlank(tmsStoreCustomer.getCustomerCode()) ||
                        StrUtil.isNotBlank(tmsStoreCustomer.getCustomerName()) ||
                        StrUtil.isNotBlank(tmsStoreCustomer.getCustomerPhone());

        if (!hasSearchCondition) {
            // 查询条件不存时返回空数组
            page.setTotal(0);
            page.setRecords(Collections.emptyList());
            return R.ok(page);
        }

        LambdaQueryWrapper<TmsStoreCustomerEntity> wrapper = Wrappers.<TmsStoreCustomerEntity>lambdaQuery()
                .like(StrUtil.isNotBlank(tmsStoreCustomer.getCustomerCode()), TmsStoreCustomerEntity::getCode, tmsStoreCustomer.getCustomerCode())
                .like(StrUtil.isNotBlank(tmsStoreCustomer.getCustomerName()), TmsStoreCustomerEntity::getName, tmsStoreCustomer.getCustomerName())
                .eq(StrUtil.isNotBlank(tmsStoreCustomer.getCustomerPhone()), TmsStoreCustomerEntity::getPhone, tmsStoreCustomer.getCustomerPhone())
                .and(w -> w.isNull(TmsStoreCustomerEntity::getStoreId)
                        .or()
                        .eq(TmsStoreCustomerEntity::getStoreId, tmsStoreCustomer.getStoreId()))
                .orderByAsc(TmsStoreCustomerEntity::getCreateTime);

        return R.ok(service.page(page, wrapper));
    }

    /**
     * 门店端添加客户信息
     */
    @Operation(summary = "门店端添加客户信息" , description = "门店端添加客户信息")
    @SysLog("门店端添加客户信息")
    @PostMapping("/storeSave")
    @PreAuthorize("@pms.hasPermission('tms_customer_user_store_add')")
    public R saveStoreCustomer(@Valid @RequestBody TmsStoreCustomerDTO tmsStoreCustomer){
        return service.saveStoreCustomer(tmsStoreCustomer);
    }

    /**
     * 管理端添加客户信息
     */
    @Operation(summary = "管理端添加客户信息" , description = "管理端添加客户信息")
    @SysLog("管理端添加客户信息")
    @PostMapping("/storeAdminSave")
    @PreAuthorize("@pms.hasPermission('tms_customer_user_admin_add')")
    public R saveStoreAdminCustomer(@Valid @RequestBody TmsStoreCustomerDTO tmsStoreCustomer){
        return service.saveStoreCustomer(tmsStoreCustomer);
    }

    /**
     * 门店端修改客户信息
     */
    @Operation(summary = "门店端修改客户信息" , description = "门店端修改客户信息")
    @SysLog("门店端修改客户信息")
    @PostMapping("/storeUpdate")
    @PreAuthorize("@pms.hasPermission('tms_customer_user_store_edit')")
    public R updateStoreCustomer(@Valid @RequestBody TmsStoreCustomerDTO tmsStoreCustomer){
        return service.updateStoreCustomer(tmsStoreCustomer);
    }

    /**
     * 管理端修改客户信息
     */
    @Operation(summary = "管理端修改客户信息" , description = "管理端修改客户信息")
    @SysLog("管理端修改客户信息")
    @PostMapping("/storeAdminUpdate")
    @PreAuthorize("@pms.hasPermission('tms_customer_user_admin_edit')")
    public R updateStoreAdminCustomer(@Valid @RequestBody TmsStoreCustomerDTO tmsStoreCustomer){
        return service.updateStoreCustomer(tmsStoreCustomer);
    }

    /**
     * 门店端禁用/启用门店客户
     */
    @Operation(summary = "门店端禁用/启用门店客户", description = "门店端禁用/启用门店客户")
    @SysLog("门店端禁用/启用门店客户")
    @PutMapping("/updateStatus")
    @PreAuthorize("@pms.hasPermission('tms_customer_store_status')")
    public R updateStatus(@RequestBody TmsStoreCustomerEntity tmsStoreCustomer) {
        return service.updateStatus(tmsStoreCustomer);
    }

    /**
     * 管理端禁用/启用门店客户
     */
    @Operation(summary = "管理端禁用/启用门店客户", description = "管理端禁用/启用门店客户")
    @SysLog("管理端禁用/启用门店客户")
    @PutMapping("/updateAdminStatus")
    @PreAuthorize("@pms.hasPermission('tms_customer_admin_status')")
    public R updateAdminStatus(@RequestBody TmsStoreCustomerEntity tmsStoreCustomer) {
        return service.updateStatus(tmsStoreCustomer);
    }

    /**
     * 管理端分页查询客户信息
     */
    @Operation(summary = "管理端分页查询客户信息" , description = "管理端分页查询客户信息" )
    @SysLog("管理端分页查询客户信息")
    @GetMapping("/adminPage" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreCustomer_admin_view')" )
    public R getTmsStoreCustomerAdminPage(@ParameterObject Page page, @ParameterObject TmsStoreCustomerPageVo tmsStoreCustomer){
        return service.getTmsStoreCustomerAdminPage(page,tmsStoreCustomer);
    }

    /**
     * 门店端修改客户密码
     */
    @Operation(summary = "门店端修改客户密码", description = "门店端修改客户密码")
    @SysLog("门店端修改客户密码")
    @PutMapping("/updateStorePwd")
    @PreAuthorize("@pms.hasPermission('tms_customer_update_store_pwd')")
    public R updateStorePwd(@RequestBody TmsStoreCustomerDTO tmsStoreDTO){
        return service.updateStoreCustomerPwd(tmsStoreDTO);
    }

    /**
     * 管理端修改客户密码
     */
    @Operation(summary = "管理端修改客户密码", description = "管理端修改客户密码")
    @SysLog("管理端修改客户密码")
    @PutMapping("/updateStoreAdminPwd")
    @PreAuthorize("@pms.hasPermission('tms_customer_update_admin_pwd')")
    public R updateStoreAdminPwd(@RequestBody TmsStoreCustomerDTO tmsStoreDTO){
        return service.updateStoreCustomerPwd(tmsStoreDTO);
    }

    /**
     * 管理端导出客户信息
     */
    @ResponseExcel
    @Operation(summary = "管理端导出客户信息" , description = "管理端导出客户信息" )
    @SysLog("管理端导出客户信息")
    @GetMapping("/adminExport" )
    @PreAuthorize("@pms.hasPermission('tms_storeCustomer_admin_export')" )
    public List<TmsStoreCustomerExcelVo> exportTmsStoreCustomerAdmin(@RequestBody TmsStoreCustomerPageVo tmsStoreCustomer){
        return service.getTmsStoreCustomerAdminExcel(tmsStoreCustomer);
    }

    /**
     * 获取所有客户
     */
    @Operation(summary = "获取所有客户", description = "获取所有客户")
    @SysLog("获取所有客户")
    @GetMapping("/getAllCustomers")
    public R<List<TmsStoreCustomerEntity>> getAllCustomers(){
        return R.ok(service.list());
    }

    /**
     * 获取当前门店下的所有客户
     */
    @Operation(summary = "获取当前门店下的所有客户", description = "获取当前门店下的所有客户")
    @SysLog("获取当前门店下的所有客户")
    @GetMapping("/getAllCustomersByUser")
//    @PreAuthorize("@pms.hasPermission('tms_storeCustomer_store_list')" )
    public R<List<TmsStoreCustomerEntity>> getAllCustomersByUser(){
        return R.ok(service.getAllCustomersByUser());
    }

    @Operation(summary = "注册客户", description = "注册客户")
    @PostMapping("/register")
    @Inner(value = false)
    public R register(@RequestBody SysUserAddDto dto) {
        return service.register(dto);
    }
}
