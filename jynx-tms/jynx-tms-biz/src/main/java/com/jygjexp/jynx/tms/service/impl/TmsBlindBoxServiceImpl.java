package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsBlindBoxEntity;
import com.jygjexp.jynx.tms.entity.TmsBlindBoxRuleEntity;
import com.jygjexp.jynx.tms.mapper.TmsBlindBoxMapper;
import com.jygjexp.jynx.tms.service.TmsBlindBoxRuleService;
import com.jygjexp.jynx.tms.service.TmsBlindBoxService;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsBlindBoxExcelVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.*;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 物流商盲盒信息
 *
 * <AUTHOR>
 * @date 2025-07-18 18:00:37
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TmsBlindBoxServiceImpl extends ServiceImpl<TmsBlindBoxMapper, TmsBlindBoxEntity> implements TmsBlindBoxService {

    private final TmsBlindBoxRuleService tmsBlindBoxRuleService;
    private final TmsBlindBoxMapper tmsBlindBoxMapper;

    // 分页查询
    @Override
    public Page<TmsBlindBoxPageVo> search(Page page, TmsBlindBoxPageVo vo) {
        LambdaQueryWrapper<TmsBlindBoxEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(vo.getCode()), TmsBlindBoxEntity::getCode, vo.getCode())
                .like(StrUtil.isNotBlank(vo.getName()), TmsBlindBoxEntity::getName, vo.getName())
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), TmsBlindBoxEntity::getIsValid, vo.getIsValid())
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()), TmsBlindBoxEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                .orderByDesc(TmsBlindBoxEntity::getCreateTime);
        return this.page(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R createBlindBox(TmsBlindBoxCreateVo createVo) {
        log.info("开始创建盲盒，参数：{}", createVo);

        try {
            // 检查盲盒代码是否已存在
            LambdaQueryWrapper<TmsBlindBoxEntity> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(TmsBlindBoxEntity::getCode, createVo.getCode());
            if (this.count(checkWrapper) > 0) {
                return R.failed("盲盒代码已存在：" + createVo.getCode());
            }

            // 创建盲盒主表记录
            TmsBlindBoxEntity blindBoxEntity = new TmsBlindBoxEntity();
            BeanUtils.copyProperties(createVo, blindBoxEntity);

            if (!this.save(blindBoxEntity)) {
                return R.failed("创建盲盒失败");
            }

            // 创建盲盒规则配置
            if (CollUtil.isNotEmpty(createVo.getRules())) {
                List<TmsBlindBoxRuleEntity> ruleEntities = createVo.getRules().stream()
                        .map(ruleVo -> {
                            TmsBlindBoxRuleEntity ruleEntity = new TmsBlindBoxRuleEntity();
                            BeanUtils.copyProperties(ruleVo, ruleEntity);
                            ruleEntity.setBlindBoxId(blindBoxEntity.getId());
                            return ruleEntity;
                        })
                        .collect(Collectors.toList());

                if (!tmsBlindBoxRuleService.saveBatch(ruleEntities)) {
                    throw new RuntimeException("创建盲盒规则配置失败");
                }
            }

            log.info("盲盒创建成功，ID：{}", blindBoxEntity.getId());
            return R.ok(blindBoxEntity.getId(), "盲盒创建成功");

        } catch (Exception e) {
            log.error("创建盲盒失败", e);
            throw new RuntimeException("创建盲盒失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateBlindBox(TmsBlindBoxEntity updateVo) {
        try {
            // 检查盲盒是否存在
            TmsBlindBoxEntity existingBlindBox = this.getById(updateVo.getId());
            if (existingBlindBox == null) {
                return R.failed("盲盒不存在，ID：" + updateVo.getId());
            }

            // 检查盲盒代码是否与其他记录冲突
            LambdaQueryWrapper<TmsBlindBoxEntity> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(TmsBlindBoxEntity::getCode, updateVo.getCode())
                    .ne(TmsBlindBoxEntity::getId, updateVo.getId());
            if (this.count(checkWrapper) > 0) {
                return R.failed("盲盒代码已存在：" + updateVo.getCode());
            }

            // 如果isValid不为空,并等于1，则需要判断启用条数，只允许启用一条
            if (ObjectUtil.isNotNull(updateVo.getIsValid()) && updateVo.getIsValid() == 1) {
                LambdaQueryWrapper<TmsBlindBoxEntity> validWrapper = Wrappers.lambdaQuery();
                validWrapper.eq(TmsBlindBoxEntity::getIsValid, 1)
                        .ne(TmsBlindBoxEntity::getId, updateVo.getId()); // 排除当前这条
                if (this.count(validWrapper) > 0) {
                    return R.failed("已有启用的盲盒，盲盒只可同时开启一个");
                }
            }

            // 更新盲盒主表记录
            TmsBlindBoxEntity blindBoxEntity = new TmsBlindBoxEntity();
            BeanUtils.copyProperties(updateVo, blindBoxEntity);

            if (!this.updateById(blindBoxEntity)) {
                return R.failed("更新盲盒失败");
            }

            // 处理规则配置更新
            if (CollUtil.isNotEmpty(updateVo.getRules())) {
                processRuleUpdates(updateVo.getId(), updateVo.getRules());
            }
            return R.ok("盲盒更新成功");
        } catch (Exception e) {
            log.error("更新盲盒失败", e);
            throw new RuntimeException("更新盲盒失败：" + e.getMessage());
        }
    }

    @Override
    public TmsBlindBoxDetailVo getBlindBoxDetail(Long id) {
        log.info("查询盲盒详情，ID：{}", id);

        // 查询盲盒基本信息
        TmsBlindBoxEntity blindBox = this.getById(id);
        if (blindBox == null) {
            throw new RuntimeException("盲盒不存在，ID：" + id);
        }

        // 查询关联的规则配置
        LambdaQueryWrapper<TmsBlindBoxRuleEntity> ruleWrapper = Wrappers.lambdaQuery();
        ruleWrapper.eq(TmsBlindBoxRuleEntity::getBlindBoxId, id)
                .orderByAsc(TmsBlindBoxRuleEntity::getCreateTime);
        List<TmsBlindBoxRuleEntity> rules = tmsBlindBoxRuleService.list(ruleWrapper);

        // 构建返回结果
        TmsBlindBoxDetailVo detailVo = new TmsBlindBoxDetailVo();
        detailVo.setBlindBox(blindBox);
        detailVo.setRules(rules);

        return detailVo;
    }

    @Override
    public List<TmsBlindBoxExcelVo> exportBlindBox(TmsBlindBoxPageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsBlindBoxEntity> wrapper = new MPJLambdaWrapper<TmsBlindBoxEntity>();
        // 如果指定了ID，则按ID导出
        if (ArrayUtil.isNotEmpty(ids)) {
            wrapper.in(TmsBlindBoxEntity::getId, CollUtil.toList(ids));
        }
        // 否则按查询条件导出
        wrapper.like(StrUtil.isNotBlank(vo.getCode()), TmsBlindBoxEntity::getCode, vo.getCode())
                .like(StrUtil.isNotBlank(vo.getName()), TmsBlindBoxEntity::getName, vo.getName())
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), TmsBlindBoxEntity::getIsValid, vo.getIsValid())
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),
                        TmsBlindBoxEntity::getCreateTime, vo.getStartTime(), vo.getEndTime());
        wrapper.orderByDesc(TmsBlindBoxEntity::getCreateTime);
        return tmsBlindBoxMapper.selectJoinList(TmsBlindBoxExcelVo.class,wrapper);
    }

    /**
     * 处理规则配置更新
     *
     * @param blindBoxId 盲盒ID
     * @param rules      规则配置列表
     */
    private void processRuleUpdates(Long blindBoxId, List<TmsBlindBoxRuleEntity> rules) {

        // 修改规则
        if (CollUtil.isEmpty(rules)) {
            throw new RuntimeException("修改规则不能为空");
        }

        // 先删除原有规则
        tmsBlindBoxRuleService.remove(new LambdaQueryWrapper<TmsBlindBoxRuleEntity>()
                .eq(TmsBlindBoxRuleEntity::getBlindBoxId, blindBoxId));

        for (TmsBlindBoxRuleEntity ruleEntity : rules) {
            ruleEntity.setBlindBoxId(blindBoxId);
            if (!tmsBlindBoxRuleService.save(ruleEntity)) {
                throw new RuntimeException("修改规则配置失败");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R cascadeDeleteBlindBox(Long[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return R.failed("删除的盲盒ID列表不能为空");
        }

        try {
            int successCount = 0;

            for (Long id : ids) {
                // 验证盲盒是否存在
                TmsBlindBoxEntity blindBox = this.getById(id);
                if (blindBox == null) {
                    log.warn("盲盒不存在，跳过删除，ID：{}", id);
                    continue;
                }

                // 先删除关联的规则配置记录（从表）
                LambdaQueryWrapper<TmsBlindBoxRuleEntity> ruleWrapper = Wrappers.lambdaQuery();
                ruleWrapper.eq(TmsBlindBoxRuleEntity::getBlindBoxId, id);

                List<TmsBlindBoxRuleEntity> existingRules = tmsBlindBoxRuleService.list(ruleWrapper);
                if (CollUtil.isNotEmpty(existingRules)) {
                    boolean ruleDeleteResult = tmsBlindBoxRuleService.remove(ruleWrapper);
                    if (!ruleDeleteResult) {
                        throw new RuntimeException("删除盲盒规则配置失败，盲盒ID：" + id);
                    }
                }

                // 再删除盲盒主表记录
                boolean blindBoxDeleteResult = this.removeById(id);
                if (!blindBoxDeleteResult) {
                    throw new RuntimeException("删除盲盒主表记录失败，盲盒ID：" + id);
                }

                successCount++;
                log.info("成功删除盲盒，ID：{}，盲盒代码：{}", id, blindBox.getCode());
            }
            return R.ok(successCount);

        } catch (Exception e) {
            log.error("级联删除盲盒失败", e);
            throw new RuntimeException("级联删除盲盒失败：" + e.getMessage());
        }
    }
}