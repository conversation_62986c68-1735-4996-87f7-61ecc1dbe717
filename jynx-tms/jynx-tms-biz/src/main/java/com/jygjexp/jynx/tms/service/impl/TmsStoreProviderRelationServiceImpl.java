package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsStoreProviderRelationEntity;
import com.jygjexp.jynx.tms.mapper.TmsStoreProviderRelationMapper;
import com.jygjexp.jynx.tms.service.TmsStoreProviderRelationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 门店快递订单供应商
 *
 * <AUTHOR>
 * @date 2025-07-16 20:24:32
 */
@Service
public class TmsStoreProviderRelationServiceImpl extends ServiceImpl<TmsStoreProviderRelationMapper, TmsStoreProviderRelationEntity> implements TmsStoreProviderRelationService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStoreOrderProvider(TmsStoreProviderRelationEntity saveBean) {
        if(null == saveBean){
            return;
        }
        baseMapper.insert(saveBean);
    }

    @Override
    public TmsStoreProviderRelationEntity getByMainEntrustedOrder(String mainEntrustedOrder) {
        return baseMapper.getByMainEntrustedOrder(mainEntrustedOrder);
    }

    @Override
    public Map<String, TmsStoreProviderRelationEntity> getRelationMap(List<String> mainEntrustedOrders) {
        return baseMapper.getRelationMap(mainEntrustedOrders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMainEntrustedOrder(String mainEntrustedOrder) {
        if(StrUtil.isBlank(mainEntrustedOrder) ){
            return;
        }
        LambdaQueryWrapper<TmsStoreProviderRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreProviderRelationEntity::getMainEntrustedOrder, mainEntrustedOrder);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public void saveStoreOrderProviderBatch(List<TmsStoreProviderRelationEntity> storeProviderRelations) {
        if(CollUtil.isEmpty(storeProviderRelations)){
            return;
        }
        baseMapper.insert(storeProviderRelations);
    }
}
