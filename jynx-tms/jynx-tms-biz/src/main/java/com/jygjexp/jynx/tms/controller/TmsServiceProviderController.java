package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsServiceProviderEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsServiceProviderService;
import com.jygjexp.jynx.tms.vo.TmsServiceProviderPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 服务商管理表
 *
 * <AUTHOR>
 * @date 2025-07-09 17:03:42
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsServiceProvider" )
@Tag(description = "tmsServiceProvider" , name = "服务商管理表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsServiceProviderController {

    private final  TmsServiceProviderService tmsServiceProviderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 服务商管理表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_view')" )
    public R getTmsServiceProviderPage(@ParameterObject Page page, @ParameterObject TmsServiceProviderPageVo vo) {
        return R.ok(tmsServiceProviderService.search(page, vo));
    }


    /**
     * 通过id查询服务商管理表
     * @param providerId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{providerId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_view')" )
    public R getById(@PathVariable("providerId" ) Long providerId) {
        return R.ok(tmsServiceProviderService.getById(providerId));
    }

    /**
     * 新增服务商管理表
     * @param tmsServiceProvider 服务商管理表
     * @return R
     */
    @Operation(summary = "新增服务商管理表" , description = "新增服务商管理表" )
    @SysLog("新增服务商管理表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_add')" )
    public R save(@RequestBody TmsServiceProviderEntity tmsServiceProvider) {
        // 判断服务商名称与服务商代码不可重复
        if (!checkName(tmsServiceProvider)) {
            return LocalizedR.failed("tms.serviceProvider.name.exists", tmsServiceProvider.getProviderName()+"/"+tmsServiceProvider.getProviderCode());
        }
        return R.ok(tmsServiceProviderService.save(tmsServiceProvider));
    }

    /**
     * 修改服务商管理表
     * @param tmsServiceProvider 服务商管理表
     * @return R
     */
    @Operation(summary = "修改服务商管理表" , description = "修改服务商管理表" )
    @SysLog("修改服务商管理表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_edit')" )
    public R updateById(@RequestBody TmsServiceProviderEntity tmsServiceProvider) {
        // 判断服务商名称与服务商代码不可重复
        if (!checkName(tmsServiceProvider)) {
            return LocalizedR.failed("tms.serviceProvider.name.exists", tmsServiceProvider.getProviderName()+"/"+tmsServiceProvider.getProviderCode());
        }
        return R.ok(tmsServiceProviderService.updateById(tmsServiceProvider));
    }

    /**
     * 通过id删除服务商管理表
     * @param ids providerId列表
     * @return R
     */
    @Operation(summary = "通过id删除服务商管理表" , description = "通过id删除服务商管理表" )
    @SysLog("通过id删除服务商管理表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsServiceProviderService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 查询全部服务商
    @Operation(summary = "查询全部服务商" , description = "查询全部服务商" )
    @GetMapping("/serviceProvider/list")
    //@PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_view')" )
    public R list() {
        LambdaQueryWrapper<TmsServiceProviderEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(TmsServiceProviderEntity::getProviderId, TmsServiceProviderEntity::getProviderName, TmsServiceProviderEntity::getProviderCode, TmsServiceProviderEntity::getIsValid).groupBy(TmsServiceProviderEntity::getProviderId);
        queryWrapper.orderByAsc(TmsServiceProviderEntity::getCreateTime);
        return R.ok(tmsServiceProviderService.list(queryWrapper));
    }


    // 判断服务商名称与服务商代码不可重复
    public Boolean checkName(TmsServiceProviderEntity tmsServiceProvider) {
        LambdaQueryWrapper<TmsServiceProviderEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.nested(wrapper ->
                wrapper.eq(TmsServiceProviderEntity::getProviderCode, tmsServiceProvider.getProviderCode())
                        .or()
                        .eq(TmsServiceProviderEntity::getProviderName, tmsServiceProvider.getProviderName())
        );
        queryWrapper.ne(ObjectUtil.isNotNull(tmsServiceProvider.getProviderId()),TmsServiceProviderEntity::getProviderId, tmsServiceProvider.getProviderId());
        TmsServiceProviderEntity byName = tmsServiceProviderService.getOne(queryWrapper,false);
        if (Objects.nonNull(byName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * 导出excel 表格
     * @param tmsServiceProvider 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
/*    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_export')" )
    public List<TmsServiceProviderEntity> export(TmsServiceProviderEntity tmsServiceProvider,Long[] ids) {
        return tmsServiceProviderService.list(Wrappers.lambdaQuery(tmsServiceProvider).in(ArrayUtil.isNotEmpty(ids), TmsServiceProviderEntity::getProviderId, ids));
    }*/
}