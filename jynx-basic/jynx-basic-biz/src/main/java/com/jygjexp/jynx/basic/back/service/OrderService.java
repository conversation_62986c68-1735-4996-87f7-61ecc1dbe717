package com.jygjexp.jynx.basic.back.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.basic.app.vo.PostMapVo;
import com.jygjexp.jynx.basic.app.vo.UnTakeOrderVo;
import com.jygjexp.jynx.basic.back.entity.PostEntity;
import com.jygjexp.jynx.basic.back.entity.SheinCodeEntity;
import com.jygjexp.jynx.basic.back.model.bo.*;
import com.jygjexp.jynx.basic.back.model.vo.*;
import com.jygjexp.jynx.basic.back.model.vo.excel.OrderExcelVo;
import com.jygjexp.jynx.basic.back.model.vo.excel.SheinCodeExportVo;
import com.jygjexp.jynx.basic.back.model.vo.excel.SheinPickSignExcelVo;
import com.jygjexp.jynx.basic.back.request.PaymentCallbackRequest;
import com.jygjexp.jynx.common.core.util.R;
import org.jetbrains.annotations.NotNull;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单Services
 */
public interface OrderService extends IService<SheinCodeEntity> {

    //处理希音订单
    JSONObject handleOrder(CreateSheinOrderBo vo);

    //处理顺丰订单
    JSONObject handleOrder(CreateSfOrderBo vo);

    //处理优时派订单
    JSONObject handleOrder(YspBo vo);

    //获取UPS面单
    String getUpsLabel(String orderNo);

    //分配驿站编码
    PostEntity allocationPostCode(String zip, String country, String province, String city, String address);

    //删除订单
    JSONObject deleteOrder(String logiNo, String token);

    SheinCodeEntity getOrderByLogiNoAndAuth(String code, Integer auth);

    //分页查询
    IPage<SheinPageBo> getPage(SheinCodePageRequestVo pageBo);


    //查询在途包裹数量
    List<SheinPageBo> getCountByDriverIdAndStatus(Integer driverId);

    //查询在途包裹数量
    List<UnTakeOrderVo> getUnTakeOrder(Integer postId);

    //查询订单
    SheinCodeEntity getOrder(String orderId);

    //包裹入站
    boolean receiveOrder(String orderNo, Integer postId, Integer employeeId);

    //驿站今日统计
    HashMap<String, Long> postStat(Integer postId);


    //包裹分类
    IPage<SheinCodeEntity> orderSort(OrderSortBo orderSortBo, Page page);

    //查询订单
    SheinCodeEntity getOrderByOrderNo(String orderNo);

    //查询订单（UPS）
    SheinCodeEntity getOrderByOrderNoYSP(String orderNo);

    //查询最近三天包裹
    List<SheinCodeEntity> getRecentOrder(Integer postId);

    //查询驿站和驿站组
    Map<String, Object> getGroupAndPost(Integer driverId);

    //查询司机驿站组以及驿站对应的包裹
    DriverDataVo getDriverOrder(Integer driverId);

    //查询各类订单数量
    List<returnPreviewVo> getOrderNum();

    //退件预览
    void returnPreview();

    //返仓扫描
    R returnScan(String orderNo, Integer warehouseId, String repeatOrder, Boolean localFlag);

    //返仓扫描（异步)
    R returnScanAsync(String orderNo, Integer warehouseId, String repeatOrder, Boolean localFlag);


    //获取地图基本信息
    List<PostMapVo> getMapInfo(String postIds);

    //司机扫描
    R driverScan(String orderNo, Integer driverId);

    //检查订单
    OldResult checkOrder(CreateSheinOrderBo bo);

    //更新订单
    R updateReturnOrder(SheinCodeEntity sheinCode);

    //创建优时派订单
    void createYpsOrder();

    //获取路由信息
    OldResult sheinCodeRoute(@NotNull SheinCodeEntity sheinCode, boolean isSmallPackage);

    //获取路由信息
    Map<String, Object> getTrackBy17Track(SheinCodeEntity sheinCodeEntity);


    //统计订单入站数量（有退件）
    void countPostData() throws InterruptedException;

    //统计订单入站数量（无退件）
     void countPostDataIsZero();

    //根据订单号查询驿站订单
    R getPostOrder(String orderNo, Integer postId);

    //更新面单状态
    void updateDamagedByOrderNo(String orderNo, Integer damaged);

    //更新图片
    void updateDamagedURL(String orderNo, String damagedURL);

    //打印优时派面单
    void printYspLabel(String id, HttpServletResponse response);

    //打印顺丰面单
    void printSfLabel(String id, HttpServletResponse response);

    //打印中大件、卡派面单
    void printKpLabel(String trackNo,String typeCode,HttpServletResponse response);

    //打印容器标签面单
    void printCageLabel(String labelNos,HttpServletResponse response);

    //打印格口编码面单
    void printGridLabel(Long[] gridIds, HttpServletResponse response);

    //退回客户仓库
    R returnCustomer(SheinPageBo pageBo);

    //查询经纬度
    String searchLocation(String postalCode);

    // 根据经纬度获取地址
    String searchCity(double lat, double lng);

    //查询谷歌是否已经请求过
    String getfindResult(String keyword);

    //导出
    List<OrderExcelVo> getExcel(SheinCodeExportVo requestVo);

    //获取驿站账单明细
    List<SheinCodeEntity> getWarehouseOrderByTime(LocalDateTime startTime, LocalDateTime endTime, Integer postId);

    //查询需要定时任务处理的返仓订单
    List<SheinCodeEntity> getOrderByJobCondition(LocalDateTime time);

    //查询需要发送短信的订单
    List<ReturnSmsOrderVo> getOrdersSendSms();

    //查询优时派订单轨迹
    JSONObject trackYsp(String nbLogiNo);

    //插入订单
    R saveOrder(OrderInsertBo orderInsertBo);

    //退件单导入订单
    R processFile(MultipartFile file);

    //接收小包订单状态
    JSONObject receiveOrderStatus(OrderStatusBo statusBo);

    //有你包裹入站
    R receiveUniOrder(String orderNo, Integer postId, Integer employeeId);

    //有你包裹签收
    R uniOrderSign(String orderNo, Integer postId, String signUrl);

    //驿站打印面单统计
    void printCount(String orderNo, Integer postId);

    //获取打印记录
    R printList(Page page, Integer postId);

    List<SheinCodeEntity> listRetuenSectionOrder();

    //小包获取数据
    Object getOrderData(String startTime, String endTime, String type);

    void supplementOrderInfo();

    //获取城市列表
    List<SheinCodeEntity> getCityList();    // 获取六大城市列表

    //接收PUD订单信息
    R pushPudOrderData(PudOrderBo pudOrderBo);

    // 统计监控寄出-签收的百分比
    R listPickSign(SheinPickSignBo requestVo);

    // 导出监控寄出-签收的数据
    List<SheinPickSignExcelVo> exportPickSign(SheinPickSignBo requestVo);

    // 推送订单到NB
    R pushOrderToNb(ApiOrder order, String apiKey, String timestamp);

    //拦截订单
    R blockOrder(String logiNo, String apiKey);

    //删除订单
    R delOrder(String logiNo, String apiKey);

    //校验包裹状态
    R checkOrderStatus(String orderNo);

    //打印面单
    R printLabel(String trackingNo, HttpServletResponse response);

    //查询轨迹接口
    R trackOrder(String trackingNo, String apiKey);

    //新订单面单转png
    void orderToPng();

    //获取打印历史
    R getPrintHistory(Page page, String postId);

    //打印卡派PUD
    void printKpPud(String trackNo, HttpServletResponse response);

    /**
     * 获取中大件派送PUD信息
     * @return
     */
    R getPud(String trackingNo, String apiKey);


    /**
     * 获取中大件派送PUD信息
     * @return
     */
    R getSubOrderPod(String trackingNo, String apiKey);



    /**
     * 换单接口
     * @param exchangeVo
     * @param apiKey
     * @return
     */
    R exchangeOrder(ExchangeVo exchangeVo, String apiKey);

    void printPickUpCode(List<Long> ids, HttpServletResponse response);


    void payResultHandel(PaymentCallbackRequest request);

    /**
     * 取消订单(USPEED)
     * @param logiNo
     * @param token
     * @return
     */
    JSONObject cancel(String logiNo, String token);


}